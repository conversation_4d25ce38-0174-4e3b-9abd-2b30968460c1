from django.urls import path

from api.views.incidents.grievance_incident.investigation.details import (
    grievance_investigation_details,
)
from api.views.incidents.grievance_incident.investigation.documents import (
    delete_grievance_investigation_documents,
    grievance_investigation_documents_list_api,
    new_grievance_investigation_document_api,
)
from api.views.incidents.grievance_incident.investigation.list import (
    grievance_investigation_list,
)
from api.views.incidents.grievance_incident.investigation.modify_grievance_investigation import (
    modify_grievance_investigation_incident,
)

from api.views.incidents.grievance_incident.investigation.reviews import (
    grievance_investigation_reviews,
    new_grievance_investigation_review,
)
from api.views.incidents.grievance_incident.investigation.update import (
    mark_grievance_investigation_incident_as_resolved,
    send_grievance_investigation_to_department,
    update_grievance_investigation,
)
from api.views.incidents.grievance_incident.investigation.delete_draft_incidents import delete_grievance_investigation_draft_incidents


urlpatterns = [
    path("", grievance_investigation_list, name="grievance_investigation_list"),
    path(
        "<int:incident_id>/",
        grievance_investigation_details,
        name="grievance_investigation_details",
    ),
    path(
        "<int:incident_id>/update/",
        update_grievance_investigation,
        name="update_grievance_investigation",
    ),
    # modify incident
    path(
        "<int:incident_id>/modify/",
        modify_grievance_investigation_incident,
        name="modify_grievance_investigation_incident",
    ),
    # documents
    path(
        "<int:incident_id>/documents/new/",
        new_grievance_investigation_document_api,
        name="new_grievance_investigation_document_api",
    ),
    path(
        "<int:incident_id>/documents/",
        grievance_investigation_documents_list_api,
        name="grievance_investigation_documents_api",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_grievance_investigation_documents,
        name="delete_grievance_investigation_documents",
    ),
    # end of documents
    path(
        "<int:grievance_investigation_id>/resolve/",
        mark_grievance_investigation_incident_as_resolved,
        name="mark_grievance_investigation_incident_as_resolved",
    ),
    # send to department
    path(
        "<int:incident_id>/send-to-department/",
        send_grievance_investigation_to_department,
        name="send_grievance_investigation_to_department",
    ),
    # end of send to department
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_grievance_investigation_review,
        name="new_grievance_investigation_review",
    ),
    path(
        "<int:incident_id>/reviews/",
        grievance_investigation_reviews,
        name="grievance_investigation_reviews",
    ),
    # end of reviews
    # deleting draft incidents
    path(
        "drafts/delete-multiple/",
        delete_grievance_investigation_draft_incidents,
        name="delete_grievance_investigation_draft_incidents",
    ),
]
