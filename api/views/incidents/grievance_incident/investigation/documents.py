from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
import os

from base.services.logging.logger import LoggingService
from documents.views import (
    handle_incident_document,
    handle_single_file,
    delete_incident_document,
)
from patient_visitor_grievance.models import GrievanceInvestigation
from patient_visitor_grievance.services.investigation_documents import GrievanceInvestigationDocumentService

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def grievance_investigation_extension_letter_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if "file" not in request.FILES:
        return Response(
            {"status": "failed", "message": "No file provided"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        file = request.FILES["file"]
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.upload_extension_letter(file)

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def grievance_investigation_response_letter_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if "file" not in request.FILES:
        return Response(
            {"status": "failed", "message": "No file provided"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        file = request.FILES["file"]
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.upload_response_letter(file)

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_investigation_extension_letter_get_api(request, incident_id):
    """Get extension letter copy for grievance investigation"""
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.get_extension_letter()

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_investigation_response_letter_get_api(request, incident_id):
    """Get response letter copy for grievance investigation"""
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.get_response_letter()

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def grievance_investigation_extension_letter_delete_api(request, incident_id):
    """Delete extension letter copy for grievance investigation"""
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.delete_extension_letter()

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def grievance_investigation_response_letter_delete_api(request, incident_id):
    """Delete response letter copy for grievance investigation"""
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.delete_response_letter()

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# incident documents
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_grievance_investigation_document_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")

    if not files:
        return Response(
            {"status": "failed", "message": "No files provided"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        service = GrievanceInvestigationDocumentService(user=user, investigation_id=incident_id)
        response = service.upload_general_documents(files)

        return Response(
            {
                "status": "success" if response.success else "failed",
                "message": response.message,
                "data": response.data,
            },
            status=response.code,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_investigation_documents_list_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        documents_list = [
            {
                "id": document.id,
                "name": document.name,
                "type": document.file_type,
                "created_by": f"{document.created_by.first_name} {document.created_by.last_name}",
                "created_at": document.created_at,
            }
            for document in incident.documents.all()
        ]

        return Response(documents_list, status=status.HTTP_200_OK)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_grievance_investigation_documents(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
