# Grievance Investigation Documents Implementation

## Overview

This implementation provides comprehensive document handling for grievance investigations, specifically for the special document fields `extension_letter_copy` and `response_letter_copy`, in addition to the standard `documents` field.

## Problem Solved

Previously, grievance investigations had special document fields (`extension_letter_copy` and `response_letter_copy`) that were not properly handled through dedicated endpoints, views, services, and tests like other incident types. This implementation brings these fields up to the same standard as other incident document handling.

## Components Implemented

### 1. Service Layer (`patient_visitor_grievance/services/investigation_documents.py`)

**Class: `GrievanceInvestigationDocumentService`**

Features:
- **Permission checking**: Ensures only authorized users can modify documents
- **Extension letter management**: Upload, retrieve, delete extension letters
- **Response letter management**: Upload, retrieve, delete response letters  
- **General documents**: Handle standard document uploads
- **Error handling**: Comprehensive error handling with proper API responses

Key Methods:
- `upload_extension_letter(file)` - Upload extension letter copy
- `upload_response_letter(file)` - Upload response letter copy
- `get_extension_letter()` - Retrieve extension letter details
- `get_response_letter()` - Retrieve response letter details
- `delete_extension_letter()` - Delete extension letter
- `delete_response_letter()` - Delete response letter
- `upload_general_documents(files)` - Upload multiple general documents

### 2. Enhanced Views (`api/views/incidents/grievance_incident/investigation/documents.py`)

**New Endpoints Added:**
- `grievance_investigation_extension_letter_get_api` - GET extension letter
- `grievance_investigation_extension_letter_delete_api` - DELETE extension letter
- `grievance_investigation_response_letter_get_api` - GET response letter
- `grievance_investigation_response_letter_delete_api` - DELETE response letter

**Enhanced Existing Endpoints:**
- `grievance_investigation_extension_letter_api` - Improved error handling and validation
- `grievance_investigation_response_letter_api` - Improved error handling and validation
- `new_grievance_investigation_document_api` - Now uses the service layer

### 3. URL Patterns (`api/urls/grievance_investigation.py`)

**New URL Patterns:**
```python
# Extension letter endpoints
path("<int:incident_id>/documents/extension-letter/", get_api, name="get_extension_letter")
path("<int:incident_id>/documents/extension-letter/delete/", delete_api, name="delete_extension_letter")

# Response letter endpoints  
path("<int:incident_id>/documents/response-letter/", get_api, name="get_response_letter")
path("<int:incident_id>/documents/response-letter/delete/", delete_api, name="delete_response_letter")
```

### 4. Comprehensive Test Suite

**Service Tests (`patient_visitor_grievance/tests/test_investigation_documents.py`)**
- Permission testing for all operations
- Success and failure scenarios
- Document upload, retrieval, and deletion
- Error handling for missing investigations

**Endpoint Tests (`patient_visitor_grievance/tests/test_investigation_document_endpoints.py`)**
- API endpoint testing for all new endpoints
- Authentication and authorization testing
- File upload validation
- HTTP status code verification

**Integration Tests (`patient_visitor_grievance/tests/test_integration_documents.py`)**
- Complete workflow testing (upload → get → delete)
- Cross-endpoint permission consistency
- Error handling across the entire stack
- Service and endpoint integration

## API Endpoints

### Extension Letter Endpoints

1. **Upload Extension Letter**
   - `POST /api/grievance-investigations/{incident_id}/documents/extension-letter/new/`
   - Requires: `file` in multipart form data
   - Returns: Document details on success

2. **Get Extension Letter**
   - `GET /api/grievance-investigations/{incident_id}/documents/extension-letter/`
   - Returns: Extension letter details or 404 if not found

3. **Delete Extension Letter**
   - `DELETE /api/grievance-investigations/{incident_id}/documents/extension-letter/delete/`
   - Returns: Success confirmation or error

### Response Letter Endpoints

1. **Upload Response Letter**
   - `POST /api/grievance-investigations/{incident_id}/documents/response-letter/new/`
   - Requires: `file` in multipart form data
   - Returns: Document details on success

2. **Get Response Letter**
   - `GET /api/grievance-investigations/{incident_id}/documents/response-letter/`
   - Returns: Response letter details or 404 if not found

3. **Delete Response Letter**
   - `DELETE /api/grievance-investigations/{incident_id}/documents/response-letter/delete/`
   - Returns: Success confirmation or error

### General Documents

1. **Upload General Documents**
   - `POST /api/grievance-investigations/{incident_id}/documents/new/`
   - Requires: `files` array in multipart form data
   - Returns: Array of uploaded document details

## Permission System

The implementation uses a comprehensive permission system:

- **Super Users**: Full access to all operations
- **Admin Users**: Access within their facility
- **Manager Users**: Access within their department
- **Investigation Creators**: Access to investigations they created
- **Others**: No access (403 Forbidden)

## Error Handling

Comprehensive error handling covers:
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Investigation or document not found
- **400 Bad Request**: Missing files or invalid data
- **500 Internal Server Error**: Unexpected server errors

## Response Format

All endpoints return consistent JSON responses:

```json
{
  "status": "success|failed",
  "message": "Human readable message",
  "data": {
    "id": 123,
    "name": "document_name",
    "file_type": ".pdf",
    "document_url": "https://...",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z",
    "created_by": {
      "id": 1,
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

## Testing

Run the tests with:

```bash
# Service tests
python manage.py test patient_visitor_grievance.tests.test_investigation_documents

# Endpoint tests  
python manage.py test patient_visitor_grievance.tests.test_investigation_document_endpoints

# Integration tests
python manage.py test patient_visitor_grievance.tests.test_integration_documents

# All document tests
python manage.py test patient_visitor_grievance.tests.test_investigation_documents patient_visitor_grievance.tests.test_investigation_document_endpoints patient_visitor_grievance.tests.test_integration_documents
```

## Usage Examples

### Using the Service Directly

```python
from patient_visitor_grievance.services.investigation_documents import GrievanceInvestigationDocumentService

# Initialize service
service = GrievanceInvestigationDocumentService(
    user=request.user,
    investigation_id=investigation_id
)

# Upload extension letter
response = service.upload_extension_letter(file)
if response.success:
    print(f"Extension letter uploaded: {response.data}")

# Get extension letter
response = service.get_extension_letter()
if response.success:
    print(f"Extension letter: {response.data}")
```

### Using the API Endpoints

```javascript
// Upload extension letter
const formData = new FormData();
formData.append('file', extensionLetterFile);

fetch(`/api/grievance-investigations/${investigationId}/documents/extension-letter/new/`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.status === 'success') {
    console.log('Extension letter uploaded:', data.data);
  }
});

// Get extension letter
fetch(`/api/grievance-investigations/${investigationId}/documents/extension-letter/`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.status === 'success') {
    console.log('Extension letter:', data.data);
  }
});
```

## Benefits

1. **Consistency**: Document handling now matches other incident types
2. **Security**: Proper permission checking for all operations
3. **Reliability**: Comprehensive error handling and validation
4. **Testability**: Full test coverage for all functionality
5. **Maintainability**: Clean service layer separation
6. **Extensibility**: Easy to add new document types or operations

## Future Enhancements

Potential future improvements:
- Document versioning for extension and response letters
- Bulk operations for multiple documents
- Document preview/thumbnail generation
- Audit logging for document operations
- Document expiration and archival
