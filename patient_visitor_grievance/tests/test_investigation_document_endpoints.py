from django.test import TestCase
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch
from accounts.models import Profile
from base.models import Facility, Department
from documents.models import Document
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from patient_visitor_grievance.tests.factories import GrievanceFactory, GrievanceInvestigationFactory


class BaseTestSetup(TestCase):
    def setUp(self):
        self.client = APIClient()
        
        # Create users
        self.super_user = User.objects.create_user(
            username="superuser",
            email="<EMAIL>",
            password="testpass123",
            is_superuser=True,
        )
        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="testpass123",
        )
        self.regular_user = User.objects.create_user(
            username="regularuser",
            email="<EMAIL>",
            password="testpass123",
        )

        # Create facility and department
        self.facility = Facility.objects.create(name="Test Facility")
        self.department = Department.objects.create(name="Test Department", facility=self.facility)

        # Create profiles
        self.super_profile = Profile.objects.create(user=self.super_user, facility=self.facility)
        self.admin_profile = Profile.objects.create(user=self.admin_user, facility=self.facility)
        self.regular_profile = Profile.objects.create(user=self.regular_user, facility=self.facility)

        # Create grievance and investigation
        self.grievance = GrievanceFactory(
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )
        self.investigation = GrievanceInvestigationFactory(
            grievance_report=self.grievance,
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""
        self.client.force_authenticate(user=user)

    def create_test_file(self, filename="test.pdf", content=b"test content"):
        """Create a test file for upload"""
        return SimpleUploadedFile(
            filename,
            content,
            content_type="application/pdf"
        )


class TestExtensionLetterEndpoints(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.upload_url = reverse(
            'grievance_investigation_extension_letter_api',
            kwargs={'incident_id': self.investigation.id}
        )
        self.get_url = reverse(
            'grievance_investigation_extension_letter_get_api',
            kwargs={'incident_id': self.investigation.id}
        )
        self.delete_url = reverse(
            'grievance_investigation_extension_letter_delete_api',
            kwargs={'incident_id': self.investigation.id}
        )

    @patch('documents.views.handle_single_file')
    def test_upload_extension_letter_success(self, mock_handle_single_file):
        """Test successful extension letter upload via API"""
        self._authenticate_user(self.super_user)
        
        # Mock the handle_single_file function
        mock_document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        file = self.create_test_file("extension_letter.pdf")
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('Extension letter uploaded successfully', response.data['message'])

    def test_upload_extension_letter_no_file(self):
        """Test extension letter upload without file"""
        self._authenticate_user(self.super_user)
        
        response = self.client.post(self.upload_url, {}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('No file provided', response.data['message'])

    def test_upload_extension_letter_unauthorized(self):
        """Test extension letter upload without authentication"""
        file = self.create_test_file("extension_letter.pdf")
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_upload_extension_letter_permission_denied(self):
        """Test extension letter upload with insufficient permissions"""
        self._authenticate_user(self.regular_user)
        
        file = self.create_test_file("extension_letter.pdf")
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('permission', response.data['message'].lower())

    def test_get_extension_letter_success(self):
        """Test successful extension letter retrieval"""
        self._authenticate_user(self.super_user)
        
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        response = self.client.get(self.get_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['data']['id'], document.id)

    def test_get_extension_letter_not_found(self):
        """Test extension letter retrieval when none exists"""
        self._authenticate_user(self.super_user)
        
        response = self.client.get(self.get_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('No extension letter found', response.data['message'])

    @patch('documents.services.operations.DocumentsOperations.delete_document')
    def test_delete_extension_letter_success(self, mock_delete):
        """Test successful extension letter deletion"""
        self._authenticate_user(self.super_user)
        
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        # Mock successful deletion
        from base.services.responses import APIResponse
        mock_delete.return_value = APIResponse(
            success=True,
            message="Document deleted successfully",
            data=None,
            code=200
        )

        response = self.client.delete(self.delete_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')

    def test_delete_extension_letter_permission_denied(self):
        """Test extension letter deletion with insufficient permissions"""
        self._authenticate_user(self.regular_user)
        
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        response = self.client.delete(self.delete_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('permission', response.data['message'].lower())


class TestResponseLetterEndpoints(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.upload_url = reverse(
            'grievance_investigation_response_letter_api',
            kwargs={'incident_id': self.investigation.id}
        )
        self.get_url = reverse(
            'grievance_investigation_response_letter_get_api',
            kwargs={'incident_id': self.investigation.id}
        )
        self.delete_url = reverse(
            'grievance_investigation_response_letter_delete_api',
            kwargs={'incident_id': self.investigation.id}
        )

    @patch('documents.views.handle_single_file')
    def test_upload_response_letter_success(self, mock_handle_single_file):
        """Test successful response letter upload via API"""
        self._authenticate_user(self.super_user)
        
        # Mock the handle_single_file function
        mock_document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        file = self.create_test_file("response_letter.pdf")
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('Response letter uploaded successfully', response.data['message'])

    def test_upload_response_letter_no_file(self):
        """Test response letter upload without file"""
        self._authenticate_user(self.super_user)
        
        response = self.client.post(self.upload_url, {}, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('No file provided', response.data['message'])

    def test_get_response_letter_success(self):
        """Test successful response letter retrieval"""
        self._authenticate_user(self.super_user)
        
        # Create and assign response letter
        document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.response_letter_copy = document
        self.investigation.save()

        response = self.client.get(self.get_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['data']['id'], document.id)

    def test_get_response_letter_not_found(self):
        """Test response letter retrieval when none exists"""
        self._authenticate_user(self.super_user)
        
        response = self.client.get(self.get_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['status'], 'failed')
        self.assertIn('No response letter found', response.data['message'])

    @patch('documents.services.operations.DocumentsOperations.delete_document')
    def test_delete_response_letter_success(self, mock_delete):
        """Test successful response letter deletion"""
        self._authenticate_user(self.super_user)
        
        # Create and assign response letter
        document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.response_letter_copy = document
        self.investigation.save()

        # Mock successful deletion
        from base.services.responses import APIResponse
        mock_delete.return_value = APIResponse(
            success=True,
            message="Document deleted successfully",
            data=None,
            code=200
        )

        response = self.client.delete(self.delete_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')


class TestInvestigationNotFound(BaseTestSetup):
    def test_endpoints_with_invalid_investigation_id(self):
        """Test all endpoints with invalid investigation ID"""
        self._authenticate_user(self.super_user)
        
        invalid_id = 99999
        urls = [
            reverse('grievance_investigation_extension_letter_api', kwargs={'incident_id': invalid_id}),
            reverse('grievance_investigation_extension_letter_get_api', kwargs={'incident_id': invalid_id}),
            reverse('grievance_investigation_response_letter_api', kwargs={'incident_id': invalid_id}),
            reverse('grievance_investigation_response_letter_get_api', kwargs={'incident_id': invalid_id}),
        ]
        
        for url in urls:
            if 'get' in url:
                response = self.client.get(url)
            else:
                file = self.create_test_file("test.pdf")
                response = self.client.post(url, {'file': file}, format='multipart')
            
            self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
            self.assertEqual(response.data['status'], 'failed')
            self.assertIn('investigation not found', response.data['message'].lower())
