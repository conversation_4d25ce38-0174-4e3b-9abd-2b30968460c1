from django.test import TestCase
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch, MagicMock
from accounts.models import Profile
from base.models import Facility, Department
from documents.models import Document
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from patient_visitor_grievance.services.investigation_documents import GrievanceInvestigationDocumentService
from patient_visitor_grievance.tests.factories import GrievanceFactory, GrievanceInvestigationFactory


class BaseTestSetup(TestCase):
    def setUp(self):
        # Create users
        self.super_user = User.objects.create_user(
            username="superuser",
            email="<EMAIL>",
            password="testpass123",
            is_superuser=True,
        )
        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="testpass123",
        )
        self.regular_user = User.objects.create_user(
            username="regularuser",
            email="<EMAIL>",
            password="testpass123",
        )

        # Create facility and department
        self.facility = Facility.objects.create(name="Test Facility")
        self.department = Department.objects.create(name="Test Department", facility=self.facility)

        # Create profiles
        self.super_profile = Profile.objects.create(user=self.super_user, facility=self.facility)
        self.admin_profile = Profile.objects.create(user=self.admin_user, facility=self.facility)
        self.regular_profile = Profile.objects.create(user=self.regular_user, facility=self.facility)

        # Create grievance and investigation
        self.grievance = GrievanceFactory(
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )
        self.investigation = GrievanceInvestigationFactory(
            grievance_report=self.grievance,
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )

    def create_test_file(self, filename="test.pdf", content=b"test content"):
        """Create a test file for upload"""
        return SimpleUploadedFile(
            filename,
            content,
            content_type="application/pdf"
        )


class TestGrievanceInvestigationDocumentService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationDocumentService(
            user=self.super_user,
            investigation_id=self.investigation.id
        )

    @patch('documents.views.handle_single_file')
    def test_upload_extension_letter_success(self, mock_handle_single_file):
        """Test successful extension letter upload"""
        # Mock the handle_single_file function
        mock_document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        file = self.create_test_file("extension_letter.pdf")
        response = self.service.upload_extension_letter(file)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertEqual(response.message, "Extension letter uploaded successfully")
        
        # Refresh investigation from database
        self.investigation.refresh_from_db()
        self.assertEqual(self.investigation.extension_letter_copy, mock_document)

    @patch('documents.views.handle_single_file')
    def test_upload_response_letter_success(self, mock_handle_single_file):
        """Test successful response letter upload"""
        # Mock the handle_single_file function
        mock_document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        file = self.create_test_file("response_letter.pdf")
        response = self.service.upload_response_letter(file)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertEqual(response.message, "Response letter uploaded successfully")
        
        # Refresh investigation from database
        self.investigation.refresh_from_db()
        self.assertEqual(self.investigation.response_letter_copy, mock_document)

    def test_upload_extension_letter_permission_denied(self):
        """Test extension letter upload with insufficient permissions"""
        unauthorized_service = GrievanceInvestigationDocumentService(
            user=self.regular_user,
            investigation_id=self.investigation.id
        )
        
        file = self.create_test_file("extension_letter.pdf")
        response = unauthorized_service.upload_extension_letter(file)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())

    def test_upload_response_letter_permission_denied(self):
        """Test response letter upload with insufficient permissions"""
        unauthorized_service = GrievanceInvestigationDocumentService(
            user=self.regular_user,
            investigation_id=self.investigation.id
        )
        
        file = self.create_test_file("response_letter.pdf")
        response = unauthorized_service.upload_response_letter(file)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())

    def test_get_extension_letter_success(self):
        """Test successful extension letter retrieval"""
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        response = self.service.get_extension_letter()

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["id"], document.id)
        self.assertEqual(response.data["name"], document.name)

    def test_get_extension_letter_not_found(self):
        """Test extension letter retrieval when none exists"""
        response = self.service.get_extension_letter()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("No extension letter found", response.message)

    def test_get_response_letter_success(self):
        """Test successful response letter retrieval"""
        # Create and assign response letter
        document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.response_letter_copy = document
        self.investigation.save()

        response = self.service.get_response_letter()

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["id"], document.id)
        self.assertEqual(response.data["name"], document.name)

    def test_get_response_letter_not_found(self):
        """Test response letter retrieval when none exists"""
        response = self.service.get_response_letter()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("No response letter found", response.message)

    @patch('documents.services.operations.DocumentsOperations.delete_document')
    def test_delete_extension_letter_success(self, mock_delete):
        """Test successful extension letter deletion"""
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        # Mock successful deletion
        from base.services.responses import APIResponse
        mock_delete.return_value = APIResponse(
            success=True,
            message="Document deleted successfully",
            data=None,
            code=200
        )

        response = self.service.delete_extension_letter()

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        
        # Refresh investigation from database
        self.investigation.refresh_from_db()
        self.assertIsNone(self.investigation.extension_letter_copy)

    @patch('documents.services.operations.DocumentsOperations.delete_document')
    def test_delete_response_letter_success(self, mock_delete):
        """Test successful response letter deletion"""
        # Create and assign response letter
        document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.response_letter_copy = document
        self.investigation.save()

        # Mock successful deletion
        from base.services.responses import APIResponse
        mock_delete.return_value = APIResponse(
            success=True,
            message="Document deleted successfully",
            data=None,
            code=200
        )

        response = self.service.delete_response_letter()

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        
        # Refresh investigation from database
        self.investigation.refresh_from_db()
        self.assertIsNone(self.investigation.response_letter_copy)

    def test_delete_extension_letter_permission_denied(self):
        """Test extension letter deletion with insufficient permissions"""
        unauthorized_service = GrievanceInvestigationDocumentService(
            user=self.regular_user,
            investigation_id=self.investigation.id
        )
        
        # Create and assign extension letter
        document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        self.investigation.extension_letter_copy = document
        self.investigation.save()

        response = unauthorized_service.delete_extension_letter()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())

    def test_delete_extension_letter_not_found(self):
        """Test extension letter deletion when none exists"""
        response = self.service.delete_extension_letter()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("No extension letter found", response.message)

    def test_investigation_not_found(self):
        """Test service behavior when investigation doesn't exist"""
        invalid_service = GrievanceInvestigationDocumentService(
            user=self.super_user,
            investigation_id=99999
        )
        
        file = self.create_test_file("test.pdf")
        response = invalid_service.upload_extension_letter(file)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("investigation not found", response.message.lower())

    @patch('documents.views.handle_incident_document')
    def test_upload_general_documents_success(self, mock_handle_incident_document):
        """Test successful general documents upload"""
        mock_handle_incident_document.return_value = {
            "files": [
                {
                    "id": 1,
                    "name": "document1",
                    "file_type": ".pdf",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-01T00:00:00Z",
                    "url": "http://test.com/doc1.pdf"
                }
            ]
        }

        files = [self.create_test_file("doc1.pdf"), self.create_test_file("doc2.pdf")]
        response = self.service.upload_general_documents(files)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertEqual(response.message, "Documents uploaded successfully")
        mock_handle_incident_document.assert_called_once()

    def test_upload_general_documents_permission_denied(self):
        """Test general documents upload with insufficient permissions"""
        unauthorized_service = GrievanceInvestigationDocumentService(
            user=self.regular_user,
            investigation_id=self.investigation.id
        )
        
        files = [self.create_test_file("doc1.pdf")]
        response = unauthorized_service.upload_general_documents(files)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())
