"""
Integration tests for grievance investigation document handling.
Tests the complete flow from service to endpoint to database.
"""
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch
from accounts.models import Profile
from base.models import Facility, Department
from documents.models import Document
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from patient_visitor_grievance.tests.factories import GrievanceFactory, GrievanceInvestigationFactory
from patient_visitor_grievance.services.investigation_documents import GrievanceInvestigationDocumentService


class IntegrationTestSetup(TransactionTestCase):
    def setUp(self):
        self.client = APIClient()
        
        # Create users
        self.super_user = User.objects.create_user(
            username="superuser",
            email="<EMAIL>",
            password="testpass123",
            is_superuser=True,
        )
        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="testpass123",
        )
        self.regular_user = User.objects.create_user(
            username="regularuser",
            email="<EMAIL>",
            password="testpass123",
        )

        # Create facility and department
        self.facility = Facility.objects.create(name="Test Facility")
        self.department = Department.objects.create(name="Test Department", facility=self.facility)

        # Create profiles
        self.super_profile = Profile.objects.create(user=self.super_user, facility=self.facility)
        self.admin_profile = Profile.objects.create(user=self.admin_user, facility=self.facility)
        self.regular_profile = Profile.objects.create(user=self.regular_user, facility=self.facility)

        # Create grievance and investigation
        self.grievance = GrievanceFactory(
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )
        self.investigation = GrievanceInvestigationFactory(
            grievance_report=self.grievance,
            created_by=self.super_user,
            report_facility=self.facility,
            department=self.department,
        )

    def create_test_file(self, filename="test.pdf", content=b"test content"):
        """Create a test file for upload"""
        return SimpleUploadedFile(
            filename,
            content,
            content_type="application/pdf"
        )


class TestDocumentWorkflow(IntegrationTestSetup):
    """Test the complete document workflow from upload to deletion"""

    @patch('documents.views.handle_single_file')
    def test_extension_letter_complete_workflow(self, mock_handle_single_file):
        """Test complete extension letter workflow: upload -> get -> delete"""
        self.client.force_authenticate(user=self.super_user)
        
        # Mock document creation
        mock_document = Document.objects.create(
            name="extension_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        # Step 1: Upload extension letter
        upload_url = reverse(
            'grievance_investigation_extension_letter_api',
            kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
        )
        file = self.create_test_file("extension_letter.pdf")
        upload_response = self.client.post(upload_url, {'file': file}, format='multipart')
        
        self.assertEqual(upload_response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(upload_response.data['status'], 'success')
        
        # Verify document was saved to investigation
        self.investigation.refresh_from_db()
        self.assertIsNotNone(self.investigation.extension_letter_copy)
        self.assertEqual(self.investigation.extension_letter_copy.id, mock_document.id)

        # Step 2: Retrieve extension letter
        get_url = reverse(
            'grievance_investigation_extension_letter_get_api',
            kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
        )
        get_response = self.client.get(get_url)
        
        self.assertEqual(get_response.status_code, status.HTTP_200_OK)
        self.assertEqual(get_response.data['status'], 'success')
        self.assertEqual(get_response.data['data']['id'], mock_document.id)
        self.assertEqual(get_response.data['data']['name'], mock_document.name)

        # Step 3: Delete extension letter
        with patch('documents.services.operations.DocumentsOperations.delete_document') as mock_delete:
            from base.services.responses import APIResponse
            mock_delete.return_value = APIResponse(
                success=True,
                message="Document deleted successfully",
                data=None,
                code=200
            )
            
            delete_url = reverse(
                'grievance_investigation_extension_letter_delete_api',
                kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
            )
            delete_response = self.client.delete(delete_url)
            
            self.assertEqual(delete_response.status_code, status.HTTP_200_OK)
            self.assertEqual(delete_response.data['status'], 'success')
            
            # Verify document was removed from investigation
            self.investigation.refresh_from_db()
            self.assertIsNone(self.investigation.extension_letter_copy)

    @patch('documents.views.handle_single_file')
    def test_response_letter_complete_workflow(self, mock_handle_single_file):
        """Test complete response letter workflow: upload -> get -> delete"""
        self.client.force_authenticate(user=self.super_user)
        
        # Mock document creation
        mock_document = Document.objects.create(
            name="response_letter",
            file_type=".pdf",
            created_by=self.super_user,
            document_url="http://test.com/doc.pdf"
        )
        mock_handle_single_file.return_value = (mock_document, True, "Success")

        # Step 1: Upload response letter
        upload_url = reverse(
            'grievance_investigation_response_letter_api',
            kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
        )
        file = self.create_test_file("response_letter.pdf")
        upload_response = self.client.post(upload_url, {'file': file}, format='multipart')
        
        self.assertEqual(upload_response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(upload_response.data['status'], 'success')
        
        # Verify document was saved to investigation
        self.investigation.refresh_from_db()
        self.assertIsNotNone(self.investigation.response_letter_copy)
        self.assertEqual(self.investigation.response_letter_copy.id, mock_document.id)

        # Step 2: Retrieve response letter
        get_url = reverse(
            'grievance_investigation_response_letter_get_api',
            kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
        )
        get_response = self.client.get(get_url)
        
        self.assertEqual(get_response.status_code, status.HTTP_200_OK)
        self.assertEqual(get_response.data['status'], 'success')
        self.assertEqual(get_response.data['data']['id'], mock_document.id)

        # Step 3: Delete response letter
        with patch('documents.services.operations.DocumentsOperations.delete_document') as mock_delete:
            from base.services.responses import APIResponse
            mock_delete.return_value = APIResponse(
                success=True,
                message="Document deleted successfully",
                data=None,
                code=200
            )
            
            delete_url = reverse(
                'grievance_investigation_response_letter_delete_api',
                kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
            )
            delete_response = self.client.delete(delete_url)
            
            self.assertEqual(delete_response.status_code, status.HTTP_200_OK)
            self.assertEqual(delete_response.data['status'], 'success')
            
            # Verify document was removed from investigation
            self.investigation.refresh_from_db()
            self.assertIsNone(self.investigation.response_letter_copy)

    def test_permission_consistency_across_endpoints(self):
        """Test that permissions are consistently enforced across all endpoints"""
        self.client.force_authenticate(user=self.regular_user)
        
        file = self.create_test_file("test.pdf")
        
        # Test all upload endpoints with insufficient permissions
        upload_urls = [
            reverse('grievance_investigation_extension_letter_api', kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}),
            reverse('grievance_investigation_response_letter_api', kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}),
            reverse('grievance_investigation_documents_api', kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}),
        ]
        
        for url in upload_urls:
            if 'grievance_investigation_documents_api' in url:
                response = self.client.post(url, {'files': [file]}, format='multipart')
            else:
                response = self.client.post(url, {'file': file}, format='multipart')
            
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
            self.assertEqual(response.data['status'], 'failed')
            self.assertIn('permission', response.data['message'].lower())

    @patch('documents.views.handle_incident_document')
    def test_general_documents_upload_workflow(self, mock_handle_incident_document):
        """Test general documents upload workflow"""
        self.client.force_authenticate(user=self.super_user)
        
        # Mock the handle_incident_document function
        mock_handle_incident_document.return_value = {
            "files": [
                {
                    "id": 1,
                    "name": "document1",
                    "file_type": ".pdf",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-01T00:00:00Z",
                    "url": "http://test.com/doc1.pdf"
                },
                {
                    "id": 2,
                    "name": "document2",
                    "file_type": ".docx",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-01T00:00:00Z",
                    "url": "http://test.com/doc2.docx"
                }
            ]
        }

        # Upload multiple general documents
        upload_url = reverse(
            'grievance_investigation_documents_api',
            kwargs={'id': self.grievance.id, 'investigation_id': self.investigation.id}
        )
        files = [
            self.create_test_file("doc1.pdf"),
            self.create_test_file("doc2.docx", b"docx content")
        ]
        response = self.client.post(upload_url, {'files': files}, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['message'], 'Documents uploaded successfully')
        self.assertIn('files', response.data['data'])
        self.assertEqual(len(response.data['data']['files']), 2)

    def test_service_direct_usage(self):
        """Test using the service directly without going through endpoints"""
        service = GrievanceInvestigationDocumentService(
            user=self.super_user,
            investigation_id=self.investigation.id
        )

        # Test getting non-existent documents
        ext_response = service.get_extension_letter()
        self.assertFalse(ext_response.success)
        self.assertEqual(ext_response.code, 404)

        resp_response = service.get_response_letter()
        self.assertFalse(resp_response.success)
        self.assertEqual(resp_response.code, 404)

        # Test permission checking
        unauthorized_service = GrievanceInvestigationDocumentService(
            user=self.regular_user,
            investigation_id=self.investigation.id
        )
        
        with patch('documents.views.handle_single_file') as mock_handle:
            mock_handle.return_value = (Document(), True, "Success")
            file = self.create_test_file("test.pdf")
            
            response = unauthorized_service.upload_extension_letter(file)
            self.assertFalse(response.success)
            self.assertEqual(response.code, 403)

    def test_error_handling_invalid_investigation(self):
        """Test error handling when investigation doesn't exist"""
        self.client.force_authenticate(user=self.super_user)
        
        invalid_id = 99999
        file = self.create_test_file("test.pdf")
        
        # Test all endpoints with invalid investigation ID
        urls_and_methods = [
            (reverse('grievance_investigation_extension_letter_api', kwargs={'id': invalid_id, 'investigation_id': invalid_id}), 'post'),
            (reverse('grievance_investigation_extension_letter_get_api', kwargs={'id': invalid_id, 'investigation_id': invalid_id}), 'get'),
            (reverse('grievance_investigation_response_letter_api', kwargs={'id': invalid_id, 'investigation_id': invalid_id}), 'post'),
            (reverse('grievance_investigation_response_letter_get_api', kwargs={'id': invalid_id, 'investigation_id': invalid_id}), 'get'),
        ]
        
        for url, method in urls_and_methods:
            if method == 'post':
                response = self.client.post(url, {'file': file}, format='multipart')
            else:
                response = self.client.get(url)
            
            self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
            self.assertEqual(response.data['status'], 'failed')
            self.assertIn('investigation not found', response.data['message'].lower())
